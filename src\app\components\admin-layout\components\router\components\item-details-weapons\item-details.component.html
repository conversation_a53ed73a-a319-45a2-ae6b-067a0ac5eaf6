
<div class="card list-header"
     style="height: 70px; margin: 15px 30px 0;">
  <div class="header">
    <button class="{{activeTab === 'weapons' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('weapons')">
      Weapons
    </button>
    <button class="{{activeTab === 'weaponRarity' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('weaponRarity')" style="margin-left: 5px;">
      Weapons Rarity
    </button>
    <button class="{{activeTab === 'weaponsWLBase' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
            (click)="switchToTab('weaponsWLBase')" style="margin-left: 5px;">
      Weapons WLBase
    </button>
  </div>
</div>

<app-weapons-generator *ngIf="activeTab === 'weapons'"></app-weapons-generator>
<app-weapon-rarity *ngIf="activeTab === 'weaponRarity'"></app-weapon-rarity>
<app-weapons-wlbase-generator *ngIf="activeTab === 'weaponsWLBase'"></app-weapons-wlbase-generator>

